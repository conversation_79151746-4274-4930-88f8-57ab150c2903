
'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import HijriDate from 'hijri-date';
import { ArrowDown } from 'lucide-react';

// --- Validation Schemas ---
const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const gregorianSchema = z.object({
  day: requiredNumber('اليوم مطلوب').int().min(1).max(31),
  month: requiredNumber('الشهر مطلوب').int().min(1).max(12),
  year: requiredNumber('السنة مطلوبة').int().min(1),
}).refine(data => {
    try {
        const date = new Date(data.year, data.month - 1, data.day);
        return date.getFullYear() === data.year && date.getMonth() === data.month - 1 && date.getDate() === data.day;
    } catch { return false; }
}, { message: 'التاريخ الميلادي غير صالح.', path: ['day'] });

const hijriSchema = z.object({
  day: requiredNumber('اليوم مطلوب').int().min(1).max(30),
  month: requiredNumber('الشهر مطلوب').int().min(1).max(12),
  year: requiredNumber('السنة مطلوبة').int().min(1),
}).refine(data => {
    try {
        new HijriDate(data.year, data.month, data.day).toGregorian();
        return true;
    } catch { return false; }
}, { message: 'التاريخ الهجري غير صالح.', path: ['day'] });

// --- Reusable Components ---
const DateFields = ({ control, namePrefix }: { control: Control<any>, namePrefix: 'gregorian' | 'hijri' }) => (
    <div className="grid grid-cols-3 gap-4">
        <FormField
            control={control}
            name={`${namePrefix}.day`}
            render={({ field }) => (
                <FormItem>
                    <FormLabel>اليوم</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder="23" {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name={`${namePrefix}.month`}
            render={({ field }) => (
                <FormItem>
                    <FormLabel>الشهر</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder="9" {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name={`${namePrefix}.year`}
            render={({ field }) => (
                <FormItem>
                    <FormLabel>السنة</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder="1990" {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    </div>
);

// --- Main Tool Component ---
export function DateConverterTool() {
  const [hijriResult, setHijriResult] = useState<string>('');
  const [gregorianResult, setGregorianResult] = useState<string>('');

  const gregorianForm = useForm<z.infer<typeof gregorianSchema>>({
    resolver: zodResolver(gregorianSchema),
    defaultValues: {
      day: undefined,
      month: undefined,
      year: undefined,
    }
  });

  const hijriForm = useForm<z.infer<typeof hijriSchema>>({
    resolver: zodResolver(hijriSchema),
    defaultValues: {
      day: undefined,
      month: undefined,
      year: undefined,
    }
  });

  const onGregorianSubmit = (data: z.infer<typeof gregorianSchema>) => {
    const gregorianDate = new Date(data.year, data.month - 1, data.day);
    const hijri = new HijriDate(gregorianDate);
    const formattedHijri = new Intl.DateTimeFormat('ar-SA-u-ca-islamic', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    }).format(gregorianDate);
    setHijriResult(formattedHijri);
  };

  const onHijriSubmit = (data: z.infer<typeof hijriSchema>) => {
    const hijriDate = new HijriDate(data.year, data.month, data.day);
    const gregorian = hijriDate.toGregorian();
    const formattedGregorian = new Intl.DateTimeFormat('ar-SA', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    }).format(gregorian);
    setGregorianResult(formattedGregorian);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>محول التاريخ</CardTitle>
        <CardDescription>التحويل بين التاريخ الميلادي والهجري بسهولة ودقة.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">

        {/* From Gregorian to Hijri */}
        <div className="p-6 border rounded-lg bg-muted/30">
          <h3 className="text-lg font-semibold mb-4">من ميلادي إلى هجري</h3>
          <Form {...gregorianForm}>
            <form onSubmit={gregorianForm.handleSubmit(onGregorianSubmit)} className="space-y-4">
              <DateFields control={gregorianForm.control} namePrefix="gregorian" />
              <Button type="submit" className="w-full">
                <ArrowDown className="ml-2 h-4 w-4" />
                تحويل إلى هجري
              </Button>
            </form>
          </Form>
          {hijriResult && (
            <div className="mt-4 p-4 bg-primary/10 rounded-lg w-full text-center">
              <p className="text-sm text-muted-foreground">التاريخ الهجري الموافق</p>
              <p className="text-xl font-bold font-mono text-primary">{hijriResult}</p>
            </div>
          )}
        </div>

        {/* From Hijri to Gregorian */}
        <div className="p-6 border rounded-lg bg-muted/30">
          <h3 className="text-lg font-semibold mb-4">من هجري إلى ميلادي</h3>
          <Form {...hijriForm}>
            <form onSubmit={hijriForm.handleSubmit(onHijriSubmit)} className="space-y-4">
              <DateFields control={hijriForm.control} namePrefix="hijri" />
              <Button type="submit" className="w-full">
                <ArrowDown className="ml-2 h-4 w-4" />
                تحويل إلى ميلادي
              </Button>
            </form>
          </Form>
          {gregorianResult && (
            <div className="mt-4 p-4 bg-primary/10 rounded-lg w-full text-center">
              <p className="text-sm text-muted-foreground">التاريخ الميلادي الموافق</p>
              <p className="text-xl font-bold font-mono text-primary">{gregorianResult}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
