
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import HijriDate from 'hijri-date';
import { ArrowDown, RotateCcw } from 'lucide-react';

// --- Validation Schemas ---
const dateSchema = z.object({
  day: z.coerce.number().min(1).max(31),
  month: z.coerce.number().min(1).max(12),
  year: z.coerce.number().min(1),
});

// --- Reusable Components ---
const DateFields = ({ control, isHijri = false }: { control: any, isHijri?: boolean }) => {
  const dayPlaceholder = isHijri ? "15" : "23";
  const monthPlaceholder = isHijri ? "9" : "12";
  const yearPlaceholder = isHijri ? "1445" : "2024";

  return (
    <div className="grid grid-cols-3 gap-4">
        <FormField
            control={control}
            name="day"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>اليوم</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder={dayPlaceholder} {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="month"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>الشهر</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder={monthPlaceholder} {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
        <FormField
            control={control}
            name="year"
            render={({ field }) => (
                <FormItem>
                    <FormLabel>السنة</FormLabel>
                    <FormControl>
                        <Input type="number" placeholder={yearPlaceholder} {...field} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    </div>
  );
};

// --- Main Tool Component ---
export function DateConverterTool() {
  const [hijriResult, setHijriResult] = useState<string>('');
  const [gregorianResult, setGregorianResult] = useState<string>('');

  // دالة لمسح النتائج
  const clearResults = () => {
    setHijriResult('');
    setGregorianResult('');
    gregorianForm.reset();
    hijriForm.reset();
  };

  const gregorianForm = useForm<z.infer<typeof dateSchema>>({
    resolver: zodResolver(dateSchema),
    defaultValues: {
      day: '',
      month: '',
      year: '',
    }
  });

  const hijriForm = useForm<z.infer<typeof dateSchema>>({
    resolver: zodResolver(dateSchema),
    defaultValues: {
      day: '',
      month: '',
      year: '',
    }
  });

  const onGregorianSubmit = (data: z.infer<typeof dateSchema>) => {
    try {
      const gregorianDate = new Date(data.year, data.month - 1, data.day);
      const hijri = new HijriDate(gregorianDate);

      const hijriMonths = [
        "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الآخرة",
        "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
      ];

      const formattedHijri = `${hijri.getDate()} ${hijriMonths[hijri.getMonth() - 1]} ${hijri.getFullYear()}هـ`;
      setHijriResult(formattedHijri);
    } catch (error) {
      setHijriResult('خطأ في التحويل');
    }
  };

  const onHijriSubmit = (data: z.infer<typeof dateSchema>) => {
    try {
      const hijriDate = new HijriDate(data.year, data.month, data.day);
      const gregorian = hijriDate.toGregorian();

      const gregorianMonths = [
        "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
        "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
      ];

      const formattedGregorian = `${gregorian.getDate()} ${gregorianMonths[gregorian.getMonth()]} ${gregorian.getFullYear()}م`;
      setGregorianResult(formattedGregorian);
    } catch (error) {
      setGregorianResult('خطأ في التحويل');
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          محول التاريخ
          {(hijriResult || gregorianResult) && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearResults}
              className="text-xs"
            >
              <RotateCcw className="ml-1 h-3 w-3" />
              مسح النتائج
            </Button>
          )}
        </CardTitle>
        <CardDescription>
          التحويل بين التاريخ الميلادي والهجري بسهولة ودقة. أدخل التاريخ في أي من النظامين للحصول على التاريخ المقابل.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">

        {/* From Gregorian to Hijri */}
        <div className="p-6 border rounded-lg bg-muted/30">
          <h3 className="text-lg font-semibold mb-4">من ميلادي إلى هجري</h3>
          <Form {...gregorianForm}>
            <form onSubmit={gregorianForm.handleSubmit(onGregorianSubmit)} className="space-y-4">
              <DateFields control={gregorianForm.control} isHijri={false} />
              <Button type="submit" className="w-full">
                <ArrowDown className="ml-2 h-4 w-4" />
                تحويل إلى هجري
              </Button>
            </form>
          </Form>
          {hijriResult && (
            <div className="mt-4 p-4 bg-primary/10 rounded-lg w-full text-center">
              <p className="text-sm text-muted-foreground">التاريخ الهجري الموافق</p>
              <p className="text-xl font-bold font-mono text-primary">{hijriResult}</p>
            </div>
          )}
        </div>

        {/* From Hijri to Gregorian */}
        <div className="p-6 border rounded-lg bg-muted/30">
          <h3 className="text-lg font-semibold mb-4">من هجري إلى ميلادي</h3>
          <Form {...hijriForm}>
            <form onSubmit={hijriForm.handleSubmit(onHijriSubmit)} className="space-y-4">
              <DateFields control={hijriForm.control} isHijri={true} />
              <Button type="submit" className="w-full">
                <ArrowDown className="ml-2 h-4 w-4" />
                تحويل إلى ميلادي
              </Button>
            </form>
          </Form>
          {gregorianResult && (
            <div className="mt-4 p-4 bg-primary/10 rounded-lg w-full text-center">
              <p className="text-sm text-muted-foreground">التاريخ الميلادي الموافق</p>
              <p className="text-xl font-bold font-mono text-primary">{gregorianResult}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
