// اختبار بسيط للتحويل بين التاريخ الميلادي والهجري
const HijriDate = require('hijri-date');

console.log('=== اختبار التحويل من الميلادي إلى الهجري ===');

// تاريخ ميلادي معروف: 1 يناير 2024
const gregorianDate = new Date(2024, 0, 1); // 1 يناير 2024
console.log('التاريخ الميلادي:', gregorianDate.toLocaleDateString('ar-SA'));

const hijri = new HijriDate(gregorianDate);
console.log('التاريخ الهجري المحول:', `${hijri.getDate()} ${getHijriMonthName(hijri.getMonth())} ${hijri.getFullYear()}هـ`);

console.log('\n=== اختبار التحويل من الهجري إلى الميلادي ===');

// تاريخ هجري معروف: 1 محرم 1446
const hijriDate = new HijriDate(1446, 1, 1); // 1 محرم 1446
const gregorianConverted = hijriDate.toGregorian();
console.log('التاريخ الهجري:', `1 محرم 1446هـ`);
console.log('التاريخ الميلادي المحول:', gregorianConverted.toLocaleDateString('ar-SA'));

console.log('\n=== اختبار تواريخ أخرى ===');

// تاريخ ميلادي آخر: 23 سبتمبر 1990
const testDate = new Date(1990, 8, 23); // 23 سبتمبر 1990
const testHijri = new HijriDate(testDate);
console.log('23 سبتمبر 1990 =', `${testHijri.getDate()} ${getHijriMonthName(testHijri.getMonth())} ${testHijri.getFullYear()}هـ`);

// دالة مساعدة لأسماء الشهور الهجرية
function getHijriMonthName(monthNumber) {
  const hijriMonths = [
    "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الآخرة",
    "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
  ];
  return hijriMonths[monthNumber - 1];
}
